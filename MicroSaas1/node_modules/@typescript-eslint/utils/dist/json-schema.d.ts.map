{"version": 3, "file": "json-schema.d.ts", "sourceRoot": "", "sources": ["../src/json-schema.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAMH;;GAEG;AACH,MAAM,MAAM,mBAAmB,GAC3B,KAAK,GACL,OAAO,GACP,SAAS,GACT,SAAS,GACT,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,QAAQ,CAAC;AAEb;;GAEG;AACH,MAAM,MAAM,eAAe,GAAG,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;AAE/D,MAAM,MAAM,uBAAuB,GAC/B,eAAe,GACf,gBAAgB,GAChB,iBAAiB,CAAC;AAKtB,MAAM,WAAW,iBAAiB;IAChC,CAAC,GAAG,EAAE,MAAM,GAAG,uBAAuB,CAAC;CACxC;AAKD,MAAM,WAAW,gBAAiB,SAAQ,KAAK,CAAC,uBAAuB,CAAC;CAAG;AAE3E;;;;;;;;;;;;GAYG;AACH,MAAM,MAAM,kBAAkB,GAAG,MAAM,CAAC;AAExC;;;GAGG;AACH,MAAM,MAAM,WAAW,GACnB,sBAAsB,GACtB,sBAAsB,GACtB,oBAAoB,GACpB,sBAAsB,GACtB,wBAAwB,GACxB,sBAAsB,GACtB,qBAAqB,GACrB,uBAAuB,GACvB,uBAAuB,GACvB,sBAAsB,GACtB,oBAAoB,GACpB,uBAAuB,CAAC;AAE5B,UAAU,eAAe;IACvB,EAAE,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAExB,OAAO,CAAC,EAAE,kBAAkB,GAAG,SAAS,CAAC;IAEzC;;OAEG;IACH,IAAI,CAAC,EAAE,mBAAmB,GAAG,mBAAmB,EAAE,GAAG,SAAS,CAAC;IAE/D;;;;;;;;;;;;;;OAcG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE1B;;;;;OAKG;IACH,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE3B;;;;;OAKG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAEjC;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,GAAG,SAAS,CAAC;IACtD;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,GAAG,SAAS,CAAC;IAEhD;;;;;;;;;;;;;;;OAeG;IACH,OAAO,CAAC,EAAE,MAAM,EAAE,GAAG,MAAM,GAAG,SAAS,CAAC;IAExC;;OAEG;IACH,OAAO,CAAC,EAAE,uBAAuB,GAAG,SAAS,CAAC;IAE9C;;;;;;OAMG;IACH,QAAQ,CAAC,EAAE,MAAM,EAAE,GAAG,OAAO,GAAG,SAAS,CAAC;IAE1C;;OAEG;IACH,GAAG,CAAC,EAAE,WAAW,GAAG,SAAS,CAAC;IAC9B;;OAEG;IACH,KAAK,CAAC,EAAE,WAAW,EAAE,GAAG,SAAS,CAAC;IAClC;;OAEG;IACH,KAAK,CAAC,EAAE,WAAW,EAAE,GAAG,SAAS,CAAC;IAClC;;OAEG;IACH,KAAK,CAAC,EAAE,WAAW,EAAE,GAAG,SAAS,CAAC;CACnC;AAED,MAAM,WAAW,oBAAqB,SAAQ,eAAe;IAC3D,IAAI,CAAC,EAAE,SAAS,CAAC;IACjB,IAAI,EAAE,MAAM,CAAC;CACd;AAED,MAAM,WAAW,sBAAuB,SAAQ,eAAe;IAC7D,IAAI,CAAC,EAAE,SAAS,CAAC;IACjB,KAAK,EAAE,WAAW,EAAE,CAAC;CACtB;AAED,MAAM,WAAW,sBAAuB,SAAQ,eAAe;IAC7D,IAAI,CAAC,EAAE,SAAS,CAAC;IACjB,KAAK,EAAE,WAAW,EAAE,CAAC;CACtB;AAED,MAAM,WAAW,sBAAuB,SAAQ,eAAe;IAC7D,IAAI,CAAC,EAAE,SAAS,CAAC;IACjB,KAAK,EAAE,WAAW,EAAE,CAAC;CACtB;AAED,MAAM,WAAW,sBACf,SAAQ,IAAI,CAAC,uBAAuB,EAAE,MAAM,GAAG,MAAM,CAAC,EACpD,IAAI,CAAC,sBAAsB,EAAE,MAAM,GAAG,MAAM,CAAC,EAC7C,IAAI,CAAC,uBAAuB,EAAE,MAAM,GAAG,MAAM,CAAC,EAC9C,IAAI,CAAC,uBAAuB,EAAE,MAAM,GAAG,MAAM,CAAC,EAC9C,IAAI,CAAC,wBAAwB,EAAE,MAAM,GAAG,MAAM,CAAC,EAC/C,IAAI,CAAC,qBAAqB,EAAE,MAAM,GAAG,MAAM,CAAC,EAC5C,IAAI,CAAC,oBAAoB,EAAE,MAAM,GAAG,MAAM,CAAC;IAC7C,IAAI,EAAE,mBAAmB,EAAE,CAAC;IAC5B;;;;;;;;OAQG;IACH,IAAI,CAAC,EAAE,eAAe,EAAE,CAAC;CAC1B;AAED;;GAEG;AACH,MAAM,WAAW,uBAAwB,SAAQ,eAAe;IAC9D,IAAI,EAAE,QAAQ,CAAC;IAEf;;;;;;;;;OASG;IACH,oBAAoB,CAAC,EAAE,WAAW,GAAG,OAAO,GAAG,SAAS,CAAC;IAEzD;;;;;;;;;;;;OAYG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,GAAG,SAAS,CAAC;IAErD;;;;;;;;;;OAUG;IACH,iBAAiB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,GAAG,SAAS,CAAC;IAE5D;;;;OAIG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,WAAW,GAAG,MAAM,EAAE,CAAC,GAAG,SAAS,CAAC;IAElE;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IACnC;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CACpC;AAED;;GAEG;AACH,MAAM,WAAW,sBAAuB,SAAQ,eAAe;IAC7D,IAAI,EAAE,OAAO,CAAC;IAEd;;;;;;;;;OASG;IACH,eAAe,CAAC,EAAE,WAAW,GAAG,OAAO,GAAG,SAAS,CAAC;IAEpD;;;;;;;;;;;;;;;;;;OAkBG;IACH,KAAK,CAAC,EAAE,WAAW,GAAG,WAAW,EAAE,GAAG,SAAS,CAAC;IAEhD;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE9B;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE9B;;OAEG;IACH,WAAW,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;CACnC;AAED;;GAEG;AACH,MAAM,WAAW,uBAAwB,SAAQ,eAAe;IAC9D,IAAI,EAAE,QAAQ,CAAC;IAEf;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE/B;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE/B;;;;;;;;;;;;OAYG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE7B;;;;;;;;;;OAUG;IACH,MAAM,CAAC,EACH,WAAW,GACX,MAAM,GACN,OAAO,GACP,UAAU,GACV,MAAM,GACN,MAAM,GACN,2BAA2B,GAC3B,cAAc,GACd,OAAO,GACP,uBAAuB,GACvB,MAAM,GACN,eAAe,GACf,cAAc,GACd,KAAK,GACL,KAAK,GACL,MAAM,GACN,SAAS,CAAC;IAEd,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,SAAS,CAAC;CAC7B;AAED;;GAEG;AACH,MAAM,WAAW,uBAAwB,SAAQ,eAAe;IAC9D,IAAI,EAAE,SAAS,GAAG,QAAQ,CAAC;IAE3B;;;OAGG;IACH,UAAU,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAEhC;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE7B;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE7B;;;;;;OAMG;IACH,gBAAgB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAEvC;;;;;;OAMG;IACH,gBAAgB,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAEvC;;;;;;;;OAQG;IACH,IAAI,CAAC,EAAE,MAAM,EAAE,GAAG,SAAS,CAAC;CAC7B;AAED;;GAEG;AACH,MAAM,WAAW,wBAAyB,SAAQ,eAAe;IAC/D,IAAI,EAAE,SAAS,CAAC;IAEhB;;;;;;;;OAQG;IACH,IAAI,CAAC,EAAE,OAAO,EAAE,GAAG,SAAS,CAAC;CAC9B;AAED;;GAEG;AACH,MAAM,WAAW,qBAAsB,SAAQ,eAAe;IAC5D,IAAI,EAAE,MAAM,CAAC;IAEb;;;;;;;;OAQG;IACH,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,SAAS,CAAC;CAC3B;AAED,MAAM,WAAW,oBAAqB,SAAQ,eAAe;IAC3D,IAAI,EAAE,KAAK,CAAC;CACb"}