{"name": "ai-microsaas-boilerplate", "version": "1.0.0", "description": "MicroSaaS boilerplate with n8n and LangChain/LangGraph integration", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "postinstall": "prisma generate", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.2", "@types/node": "^20.10.0", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "next-auth": "^4.24.5", "@auth/prisma-adapter": "^1.0.12", "bcryptjs": "^2.4.3", "@types/bcryptjs": "^2.4.6", "prisma": "^5.7.0", "@prisma/client": "^5.7.0", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/themes": "^2.0.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "lucide-react": "^0.294.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "axios": "^1.6.2", "langchain": "^0.0.208", "@langchain/core": "^0.3.58", "@langchain/openai": "^0.0.34", "@langchain/anthropic": "^0.1.8", "@langchain/langgraph": "^0.3.5", "date-fns": "^2.30.0", "uuid": "^9.0.1", "@types/uuid": "^9.0.7", "lodash": "^4.17.21", "@types/lodash": "^4.14.202", "stripe": "^14.9.0", "@stripe/stripe-js": "^2.2.0", "dotenv": "^16.3.1", "zustand": "^4.4.7", "ky": "^1.1.3", "sonner": "^1.2.4"}, "devDependencies": {"tailwindcss": "^3.3.6", "postcss": "^8.4.32", "autoprefixer": "^10.4.16", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "eslint": "^8.55.0", "eslint-config-next": "^14.0.4", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "jest": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.5", "jest-environment-jsdom": "^29.7.0", "tsx": "^4.6.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}